# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Rust-to-Lean4 verification translator called `rust_to_lean`. It's a CLI tool that translates Rust projects into Lean4 verification projects by processing Rust code through multiple stages: macro expansion, type checking, and AST transformation.

## Development Commands

### Building and Running
- `cargo build` - Build the project
- `cargo run -- --rust-project-dir <path> --output-dir <path>` - Run the translator
- `cargo check` - Quick type checking without building
- `cargo clippy` - Run linter
- `cargo test` - Run tests (if any exist)

### Dependencies
The project uses `cargo expand` extensively - ensure this is installed:
```bash
cargo install cargo-expand
```

## Architecture

The application follows a staged pipeline architecture:

1. **Expansion Stage** (`src/expand.rs`):
   - Uses `cargo expand` to expand all macros in Rust source code
   - Handles both bin/lib targets and fallback to full crate expansion
   - Saves expanded files to `<output_dir>/debug/expand/`

2. **HIR Analysis Stage** (`src/hir_analysis.rs`):
   - Uses rust-analyzer APIs (`ra_ap_*` crates) for semantic analysis
   - Processes expanded files to extract type information
   - Identifies functions, let bindings, and pre/post condition attributes
   - Currently prints debug information but designed for full HIR transformation

3. **CLI Interface** (`src/cli.rs`):
   - Simple clap-based CLI with `--rust-project-dir` and `--output-dir` arguments
   - Main entry point in `src/main.rs` coordinates the pipeline stages

### Key Dependencies
- **rust-analyzer APIs**: All `ra_ap_*` crates (version 0.0.297) for Rust semantic analysis
- **clap**: Command-line argument parsing
- **anyhow**: Error handling
- **tracing**: Structured logging

## Domain Model (from readme.md)

- **Rust project**: Input directory containing Rust code
- **Verification project**: Output Lean4 project directory
- **Rust typed AST**: AST with type info and verification attributes (`#[pre]`, `#[post]`, `#![assert]`)
- **Expanded typed rust AST**: Macro-expanded version with type-checked attributes
- **RustIR AST**: Normalized intermediate representation suitable for Lean4 translation

## Current Implementation Status

The codebase implements stages 1-2 of the planned pipeline:
- ✅ Macro expansion via `cargo expand`
- ✅ Basic HIR analysis and attribute detection
- ❌ Full type checking of verification attributes
- ❌ RustIR AST generation
- ❌ Lean4 project output

The `src/util.rs` file references a `TypeCheckedAst` structure that doesn't exist in the current codebase, suggesting incomplete implementation.

## Development Notes

- The project is designed for debugging with staged output in `<output_dir>/debug/`
- Uses environment-based tracing configuration (set `RUST_LOG` for logging)
- All rust-analyzer dependencies are pinned to version 0.0.297