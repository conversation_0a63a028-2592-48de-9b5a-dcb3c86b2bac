## Spec

### Domain model

* Rust project - A directory containing a rust project. This is the input to the
  translator.
* Verification project - A directory containing a lean4 project. This is the
  output of the translator.
* Translator - A tool that takes a rust project and outputs a verification
  project.
* Rust typed AST - An AST representing the source code of a rust project. It
  contains all type information. Additionally:
  - It contains attributes #[pre = ..], #[post = ..], #![assert = ..] that
    denote preconditions, postconditions, and inline assertions.
* Expanded typed rust AST - An AST representing the expanded source code of a
  rust project. It contains all type information and all macros have been
  expanded. All attributes have been type checked.
* RustIR AST - An intermediate representation of the rust typed AST. It is a
  simplified version of the rust typed AST. It is in a format that is easier to
  reason about and translate to lean4. Specifically, it has been normalized to
  support the computation of WPs.
  - All macros have been expanded.
  - All let expressions have been normalized to `let x [= e]` form.
  - Preconditions, postconditions, and inline assertions are now represented
    directory in the AST instead of appearing as comments.

### CLI

It will be a CLI utility invoked with the following:
* --rust-project-dir: A path to a directory containing a rust project.
* --verification-project-dir: A path to an output directory. This will contain
  an output.
* --up-to-stage: A string denoting which stage we will run the translator up to.
  This is primarily for debugging purposes. By default, the 'complete' stage is
  run.
  * `expand-rust`: Expand all macros in the rust source code.
  * `typed-rust`: Runs up to `expand-rust` and type check the rust AST.
  * `translate`: Runs up to `typed-rust` and translates the expanded AST to
    the RustIR AST.
  * `complete`: Runs the entire pipeline and outputs the RustIR AST to a lean4
    project. This is the default and does not need to be explicitly specified.

### CLI app architecture

This outlines the architecture of application. It is a simple staged pipeline.
The input is a rust project. The output is a lean4 project containing the rust
IR ASTs and proof obligations.

At a high-level, here are the stages:
1. Expand all macros in the rust source code.
  - There is a function `load_rust_project` that takes a path to a rust project
    and returns a `Project` struct. This struct contains the expanded source
    code for the project and type checked AST.
2. Type check the rust AST. If it is easier to do this in the prior stage, it
  may not appear explicitly as a stage in the implementation.
  - There is a function `type_check_rust_project` that takes a `Project` struct
    and returns a `TypedProject` struct.
  - This struct contains the type checked AST and a collection of type checked
    preconditions, postconditions, and inline assertions. It will keep them in
    associative data structures in the fields `prepost` and `assertions`
    respectively.
3. Translate the expanded AST to the RustIR AST.
  - There is a function `translate_rust_to_ir` that takes a `Project` struct and
    returns a `RustIrProject` struct. This struct contains the RustIR
    ASTs and proof obligations.
  - Preconditions, postconditions, and inline assertions are now represented
    directory in the AST instead of appearing as comments.
3. Output the RustIR AST to a lean4 project.
  - There is a function `output_verification_project` that takes a
    `RustIrProject` struct and outputs a lean4 project.


Let's implement full support for the contents of a rust file. It looks like only functions are extracted.

This should include:
* Type definitions: type aliases, structs, enums
* Traits, impls
* Generics, supertypes
* Functions
* constants