#!/usr/bin/env python3
import argparse
from pathlib import Path
import sys

def gather_and_print_rs(dir_path: Path):
    # Find all .rs files recursively, sorted by path
    rs_files = sorted(dir_path.rglob('*.rs'))
    # Now find the cargo toml files.
    toml_files = sorted(dir_path.rglob('*.toml'))

    for path in rs_files:
        # Use a relative path from the base directory for the header
        rel = path.relative_to(dir_path)
        print(f'## {rel}\n')
        print('```rust')
        try:
            print(path.read_text(encoding='utf-8').rstrip())
        except Exception as e:
            print(f'<!-- error reading file: {e} -->', file=sys.stderr)
        print('```')
        print()  # blank line between files
    
    for path in toml_files:
        rel = path.relative_to(dir_path)
        print(f'## {rel}\n')
        print('```toml')
        try:
            print(path.read_text(encoding='utf-8').rstrip())
        except Exception as e:
            print(f'<!-- error reading file: {e} -->', file=sys.stderr)
        print('```')
        print()  # blank line between files

def main():
    parser = argparse.ArgumentParser(
        description='Concatenate all Rust (.rs) files in a directory into Markdown.'
    )
    parser.add_argument(
        '--dir', '-d',
        type=Path,
        required=True,
        help='Path to the root directory to search for .rs files'
    )
    args = parser.parse_args()

    if not args.dir.is_dir():
        print(f"Error: {args.dir!r} is not a directory", file=sys.stderr)
        sys.exit(1)

    gather_and_print_rs(args.dir)

if __name__ == '__main__':
    main()