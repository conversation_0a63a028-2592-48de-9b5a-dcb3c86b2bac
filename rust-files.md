## cli.rs

```rust
use clap::Parser;
use std::path::PathBuf;

#[derive(Parser, Debug)]
#[command(author, version, about)]
pub struct Cli {
    #[arg(long)]
    pub rust_project_dir: PathBuf,
    #[arg(long)]
    pub output_dir: PathBuf,
    /// If provided, dump the Rust IR to the given file path
    #[arg(long = "dump-rust-ir", value_name = "FILE")]
    pub dump_rust_ir: Option<PathBuf>,
}
```

## hir_analysis.rs

```rust
// src/hir_analysis.rs
//! Build typed HIR+types from a real Cargo workspace and convert to RustIR.

use anyhow::Result;
use std::path::Path;

use crate::rust_ir::{
    ConstantDef as IrConst, Crate as IrCrate, EnumDef as IrEnum, FunctionDef as IrFunction,
    Item as IrItem, StructDef as IrStruct, TypeAliasDef as IrT<PERSON><PERSON><PERSON><PERSON>, VariantDef as IrVariant,
    VariantPayload,
};
use ra_ap_ide::{AnalysisHost, Semantics};
use ra_ap_ide_db::RootDatabase;
use ra_ap_syntax::ast::{self, AstNode, HasArgList, HasGenericParams, HasModuleItem, HasName};

use ra_ap_hir::{
    Const, Function, GenericDef, GenericParam as HirGenericParam, HasSource,
    TypeAlias as HirTypeAlias,
};
use ra_ap_hir_def as _; // you already used it elsewhere

pub fn analyze_workspace(project_root: &Path) -> Result<IrCrate> {
    // 1) Build a real rust-analyzer DB from Cargo
    let (db, _vfs) = crate::ra_loader::load_workspace_db(project_root, /*proc-macros*/ false)?;

    // 2) Host + Analysis snapshot for high-level enumerations
    let host = AnalysisHost::with_database(db);
    let analysis = host.analysis();

    // 3) For type queries we need Semantics tied to THIS DB
    let db_ref: &RootDatabase = host.raw_database();
    let sema = Semantics::new(db_ref);

    // 4) Iterate crates -> root file -> walk top-level items
    let mut ir = IrCrate::new();

    // Fetch crates known to the analysis
    // (CrateInfo includes a crate id we can pass back into `crate_root`)
    let crates = analysis.fetch_crates().unwrap_or_default();
    for info in crates.iter() {
        let root_file = info.root_file_id;
        let source_file = analysis.parse(root_file).unwrap();
        for item in source_file.items() {
            print!("DEBUG print item: {:?}\n", item);
            match item {
                ast::Item::Fn(fn_ast) => {
                    if let Some(func) = sema.to_def(&fn_ast) {
                        ir.items
                            .push(IrItem::Function(convert_function(&sema, &func)));
                    } else {
                        ir.items
                            .push(IrItem::Function(convert_function_syntax_fallback(&fn_ast)));
                    }
                }
                ast::Item::Struct(s_ast) => {
                    if let Some(s) = sema.to_def(&s_ast) {
                        ir.items.push(IrItem::Struct(convert_struct(&sema, &s)));
                    } else {
                        ir.items
                            .push(IrItem::Struct(convert_struct_syntax_fallback(&s_ast)));
                    }
                }
                ast::Item::Enum(e_ast) => {
                    if let Some(e) = sema.to_def(&e_ast) {
                        ir.items.push(IrItem::Enum(convert_enum(&sema, &e)));
                    } else {
                        ir.items
                            .push(IrItem::Enum(convert_enum_syntax_fallback(&e_ast)));
                    }
                }
                ast::Item::Const(c_ast) => {
                    if let Some(c) = sema.to_def(&c_ast) {
                        ir.items.push(IrItem::Constant(convert_const(&sema, &c)));
                    } else {
                        ir.items
                            .push(IrItem::Constant(convert_const_syntax_fallback(&c_ast)));
                    }
                }
                ast::Item::TypeAlias(t_ast) => {
                    if let Some(t) = sema.to_def(&t_ast) {
                        ir.items
                            .push(IrItem::TypeAlias(convert_type_alias(&sema, &t)));
                    } else {
                        ir.items
                            .push(IrItem::TypeAlias(convert_type_alias_syntax_fallback(
                                &t_ast,
                            )));
                    }
                }
                _ => { /* TODO: traits, impls, mods, etc. */ }
            }
        }
        // `info.crate_id` (older versions may name it `id`); adjust if your IDE suggests a different field.
        //let crate_id = info.crate_id;
        /*if let Ok(root_file) = analysis.crate_root(crate_id) {
                // Parse to syntax for easy top-level traversal
                let source_file = analysis.parse(root_file).unwrap();

                for item in source_file.items() {
                    match item {
                        ast::Item::Fn(fn_ast) => {
                            if let Some(func) = sema.to_def(&fn_ast) {
                                ir.items.push(IrItem::Function(convert_function(&sema, &func)));
                            } else {
                                ir.items.push(IrItem::Function(convert_function_syntax_fallback(&fn_ast)));
                            }
                        }
                        ast::Item::Struct(s_ast) => {
                            if let Some(s) = sema.to_def(&s_ast) {
                                ir.items.push(IrItem::Struct(convert_struct(&sema, &s)));
                            } else {
                                ir.items.push(IrItem::Struct(convert_struct_syntax_fallback(&s_ast)));
                            }
                        }
                        ast::Item::Enum(e_ast) => {
                            if let Some(e) = sema.to_def(&e_ast) {
                                ir.items.push(IrItem::Enum(convert_enum(&sema, &e)));
                            } else {
                                ir.items.push(IrItem::Enum(convert_enum_syntax_fallback(&e_ast)));
                            }
                        }
                        ast::Item::Const(c_ast) => {
                            if let Some(c) = sema.to_def(&c_ast) {
                                ir.items.push(IrItem::Constant(convert_const(&sema, &c)));
                            } else {
                                ir.items.push(IrItem::Constant(convert_const_syntax_fallback(&c_ast)));
                            }
                        }
                        ast::Item::TypeAlias(t_ast) => {
                            if let Some(t) = sema.to_def(&t_ast) {
                                ir.items.push(IrItem::TypeAlias(convert_type_alias(&sema, &t)));
                            } else {
                                ir.items.push(IrItem::TypeAlias(convert_type_alias_syntax_fallback(&t_ast)));
                            }
                        }
                        _ => { /* TODO: traits, impls, mods, etc. */ }
                    }
                }
            }
        */
    }

    Ok(ir)
}

// --- Converters for items --- //

fn convert_function(sema: &Semantics<RootDatabase>, fnk: &Function) -> IrFunction {
    let db = sema.db;
    let name = format!("{:?}", fnk.name(db));

    // Get function signature parameters - using correct API
    let params = fnk
        .assoc_fn_params(db)
        .iter()
        .map(|param| {
            // Extract parameter name and type from Local
            let param_name = param
                .name(db)
                .map(|n| format!("{:?}", n))
                .unwrap_or_else(|| "param".to_string());
            let param_type = convert_type(sema, &param.ty());
            (param_name, param_type)
        })
        .collect();

    // Get return type using correct API
    let return_type = {
        let ret_ty = fnk.ret_type(db);
        if ret_ty.is_unit() {
            None
        } else {
            Some(convert_type(sema, &ret_ty))
        }
    };

    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*fnk).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();

    // HYBRID APPROACH: Extract function body with full type information
    let body = if let Some(source) = fnk.source(db) {
        let ast_fn = &source.value;

        if let Some(ast_body) = ast_fn.body() {
            // Use our hybrid approach for the function body
            convert_ast_block_with_types(sema, &ast_body)
        } else {
            // Function declaration without body (e.g., extern functions)
            crate::rust_ir::Block {
                statements: Vec::new(),
                tail: None,
            }
        }
    } else {
        // Fallback for functions without source information
        crate::rust_ir::Block {
            statements: Vec::new(),
            tail: None,
        }
    };

    IrFunction {
        name,
        generics,
        params,
        return_type,
        body,
    }
}

fn convert_type_alias(sema: &Semantics<RootDatabase>, alias: &HirTypeAlias) -> IrTypeAlias {
    let db = sema.db;
    let name = format!("{:?}", alias.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*alias).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let aliased = convert_type(sema, &alias.ty(db));
    IrTypeAlias {
        name,
        generics,
        aliased,
    }
}

fn convert_struct(sema: &Semantics<RootDatabase>, strukt: &ra_ap_hir::Struct) -> IrStruct {
    let db = sema.db;
    let name = format!("{:?}", strukt.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*strukt).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let fields = strukt
        .fields(db)
        .iter()
        .map(|f| crate::rust_ir::StructField {
            name: format!("{:?}", f.name(db)),
            ty: convert_type(sema, &f.ty(db)),
        })
        .collect();
    IrStruct {
        name,
        generics,
        fields,
    }
}

fn convert_enum(sema: &Semantics<RootDatabase>, enm: &ra_ap_hir::Enum) -> IrEnum {
    let db = sema.db;
    let name = format!("{:?}", enm.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*enm).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let variants = enm
        .variants(db)
        .iter()
        .map(|var| {
            // Determine variant payload by examining fields
            let fields = var.fields(db);
            let payload = if fields.is_empty() {
                VariantPayload::Unit
            } else {
                // For now, treat all non-empty variants as tuple variants
                // TODO: distinguish between tuple and struct variants
                let field_types = fields
                    .iter()
                    .map(|f| convert_type(sema, &f.ty(db)))
                    .collect();
                VariantPayload::Tuple(field_types)
            };
            IrVariant {
                name: format!("{:?}", var.name(db)),
                payload,
            }
        })
        .collect();
    IrEnum {
        name,
        generics,
        variants,
    }
}

fn convert_const(sema: &Semantics<RootDatabase>, c: &Const) -> IrConst {
    let db = sema.db;
    let name = c
        .name(db)
        .map(|n| format!("{:?}", n))
        .unwrap_or_else(|| "unnamed".to_string());
    let ty = convert_type(sema, &c.ty(db));
    // For now, use a placeholder expression - getting const expressions requires more work
    let expr = crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit);
    IrConst { name, ty, expr }
}

/// Convert HIR generic parameter to RustIR GenericParam
fn convert_generic_param(
    db: &ra_ap_ide_db::RootDatabase,
    param: &HirGenericParam,
) -> crate::rust_ir::GenericParam {
    use ra_ap_hir::GenericParam;

    match param {
        GenericParam::TypeParam(type_param) => {
            let name = format!("{:?}", type_param.name(db));
            // For now, simplified bounds handling
            let bounds = Vec::new(); // TODO: implement trait bounds conversion
            crate::rust_ir::GenericParam { name, bounds }
        }
        GenericParam::LifetimeParam(lifetime_param) => {
            let name = format!("{:?}", lifetime_param.name(db));
            crate::rust_ir::GenericParam {
                name,
                bounds: Vec::new(), // Lifetime parameters don't have trait bounds
            }
        }
        GenericParam::ConstParam(const_param) => {
            let name = format!("{:?}", const_param.name(db));
            crate::rust_ir::GenericParam {
                name,
                bounds: Vec::new(), // Const parameters don't have trait bounds in our IR
            }
        }
    }
}

/// Convert HIR types into RustIR Type\ // Updated implementation of convert_type
fn convert_type(_sema: &Semantics<RootDatabase>, ty: &ra_ap_hir::Type) -> crate::rust_ir::Type {
    use crate::rust_ir::Type;
    // Use display as a simple fallback for structural mapping
    // Simplified type display for now
    let text = format!("{:?}", ty);
    if let Some(idx) = text.find('<') {
        // Generic instantiation
        let base: Vec<String> = text[..idx].split("::").map(|s| s.to_string()).collect();
        let args_text = &text[idx + 1..text.len() - 1];
        let args = args_text
            .split(',')
            .map(|arg| {
                let arg = arg.trim().to_string();
                Type::Path(arg.split("::").map(|s| s.to_string()).collect())
            })
            .collect();
        Type::Generic {
            base: Box::new(Type::Path(base)),
            args,
        }
    } else {
        // Simple path or primitive
        let path: Vec<String> = text.split("::").map(|s| s.to_string()).collect();
        Type::Path(path)
    }
}

/// HYBRID APPROACH: Enhanced let statement conversion with full type extraction
fn convert_let_stmt_with_full_types(
    sema: &Semantics<RootDatabase>,
    let_stmt: &ra_ap_syntax::ast::LetStmt,
) -> crate::rust_ir::Stmt {
    // STEP 1: Extract name from AST (easy structure traversal)
    let name = if let Some(pat) = let_stmt.pat() {
        match pat {
            ra_ap_syntax::ast::Pat::IdentPat(ident) => ident
                .name()
                .map(|n| n.to_string())
                .unwrap_or_else(|| "unnamed".to_string()),
            _ => format!("{:?}", pat),
        }
    } else {
        "unnamed".to_string()
    };

    // STEP 2: Use Semantics to get the INFERRED type (this is the key!)
    let ty = if let Some(pat) = let_stmt.pat() {
        sema.type_of_pat(&pat).map(|type_info| {
            // Convert HIR type to our IR type - this gives us complete type info
            convert_type(sema, &type_info.original)
        })
    } else {
        None
    };

    // STEP 3: Process initializer with hybrid approach
    let init = let_stmt.initializer().map(|init_expr| {
        // We can recursively apply the hybrid approach
        // AST structure + Semantics type info for the initializer too
        let _init_type = sema.type_of_expr(&init_expr); // Full type info available!

        // For now, return a placeholder - would recurse with convert_ast_expr_with_types
        crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit)
    });

    crate::rust_ir::Stmt::Let { name, ty, init }
}

/// HYBRID APPROACH: Convert AST block with full type information
/// This is the main entry point for our hybrid approach - called from function body extraction
fn convert_ast_block_with_types(
    sema: &Semantics<RootDatabase>,
    ast_block: &ra_ap_syntax::ast::BlockExpr,
) -> crate::rust_ir::Block {
    convert_ast_block_to_ir(sema, ast_block)
}

/// Convert HIR patterns into RustIR Pattern (simplified implementation)
fn convert_pattern(
    _sema: &Semantics<RootDatabase>,
    _pat: &ra_ap_hir_def::hir::Pat,
) -> crate::rust_ir::Pattern {
    // For now, return a placeholder - full pattern conversion needs more work
    crate::rust_ir::Pattern::Wildcard
}

/// Convert AST block to RustIR Block (works with syntax AST)
fn convert_ast_block_to_ir(
    sema: &Semantics<RootDatabase>,
    ast_block: &ra_ap_syntax::ast::BlockExpr,
) -> crate::rust_ir::Block {
    let mut statements = Vec::new();
    let mut tail = None;

    // Process statements in the block
    for stmt in ast_block.statements() {
        match stmt {
            ra_ap_syntax::ast::Stmt::LetStmt(let_stmt) => {
                // Convert let statements with FULL TYPE INFORMATION
                let name = if let Some(pat) = let_stmt.pat() {
                    match pat {
                        ra_ap_syntax::ast::Pat::IdentPat(ident) => ident
                            .name()
                            .map(|n| n.to_string())
                            .unwrap_or_else(|| "unnamed".to_string()),
                        _ => format!("{:?}", pat), // Simplified pattern handling
                    }
                } else {
                    "unnamed".to_string()
                };

                // HYBRID APPROACH: Get inferred type information from Semantics
                let ty = if let Some(pat) = let_stmt.pat() {
                    // Get the inferred type even if not explicitly written!
                    sema.type_of_pat(&pat)
                        .map(|type_info| convert_type(sema, &type_info.original))
                } else if let Some(_ty_annotation) = let_stmt.ty() {
                    // Fallback: convert explicit type annotation
                    // TODO: Convert TypeRef to our Type system
                    Some(crate::rust_ir::Type::Path(vec!["explicit".to_string()]))
                } else {
                    None
                };

                let init = let_stmt
                    .initializer()
                    .map(|expr| convert_ast_expr_with_types(sema, &expr));

                statements.push(crate::rust_ir::Stmt::Let { name, ty, init });
            }
            ra_ap_syntax::ast::Stmt::ExprStmt(expr_stmt) => {
                if let Some(expr) = expr_stmt.expr() {
                    let ir_expr = convert_ast_expr_with_types(sema, &expr);
                    statements.push(crate::rust_ir::Stmt::Expr(ir_expr));
                }
            }
            ra_ap_syntax::ast::Stmt::Item(_) => {
                // Handle item statements (nested functions, etc.)
                // For now, skip these
            }
        }
    }

    // Handle tail expression
    if let Some(tail_expr) = ast_block.tail_expr() {
        tail = Some(Box::new(convert_ast_expr_with_types(sema, &tail_expr)));
    }

    crate::rust_ir::Block { statements, tail }
}

/// Convert AST expressions to RustIR expressions with full type information
/// This is our main expression conversion function using the AST + Semantics hybrid approach
fn convert_ast_expr_with_types(
    sema: &Semantics<RootDatabase>,
    ast_expr: &ra_ap_syntax::ast::Expr,
) -> crate::rust_ir::Expr {
    use crate::rust_ir::{BinOp, Expr as IrExpr, Literal as IrLit};
    use ra_ap_syntax::ast::Expr;

    // HYBRID APPROACH: Get type information from Semantics for this expression
    let _expr_type = sema.type_of_expr(ast_expr).map(|type_info| {
        // Convert HIR type to our IR type system
        convert_type(sema, &type_info.original)
    });

    // TODO: We could store this type information in our IR expressions
    // For now, we use it for type-aware conversion logic

    match ast_expr {
        Expr::Literal(lit) => {
            // Convert literal values with type awareness
            let text = lit.token().text().to_string();
            let ir_lit = if text.starts_with('"') {
                IrLit::String(text.trim_matches('"').to_string())
            } else if text == "true" {
                IrLit::Bool(true)
            } else if text == "false" {
                IrLit::Bool(false)
            } else if let Ok(int_val) = text.parse::<i64>() {
                IrLit::Int(int_val)
            } else if let Ok(float_val) = text.parse::<f64>() {
                IrLit::Float(float_val)
            } else if text.starts_with('\'') && text.len() == 3 {
                IrLit::Char(text.chars().nth(1).unwrap_or('?'))
            } else {
                IrLit::Unit
            };
            IrExpr::Literal(ir_lit)
        }
        Expr::PathExpr(path_expr) => {
            // Convert path expressions like variable references
            if let Some(path) = path_expr.path() {
                let segments = path
                    .segments()
                    .filter_map(|seg| seg.name_ref().map(|n| n.to_string()))
                    .collect();
                IrExpr::Path(segments)
            } else {
                IrExpr::Path(vec!["unknown".to_string()])
            }
        }
        Expr::CallExpr(call_expr) => {
            let callee = if let Some(expr) = call_expr.expr() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Path(vec!["unknown".to_string()]))
            };

            let args = if let Some(arg_list) = call_expr.arg_list() {
                arg_list
                    .args()
                    .map(|arg| convert_ast_expr_with_types(sema, &arg))
                    .collect()
            } else {
                Vec::new()
            };

            IrExpr::Call { callee, args }
        }
        Expr::MethodCallExpr(method_call) => {
            let receiver = if let Some(expr) = method_call.receiver() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Path(vec!["unknown".to_string()]))
            };

            let method = method_call
                .name_ref()
                .map(|n| n.to_string())
                .unwrap_or_else(|| "unknown".to_string());

            let args = if let Some(arg_list) = method_call.arg_list() {
                arg_list
                    .args()
                    .map(|arg| convert_ast_expr_with_types(sema, &arg))
                    .collect()
            } else {
                Vec::new()
            };

            IrExpr::MethodCall {
                receiver,
                method,
                args,
            }
        }
        Expr::BinExpr(bin_expr) => {
            let lhs = if let Some(expr) = bin_expr.lhs() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Literal(IrLit::Unit))
            };

            let rhs = if let Some(expr) = bin_expr.rhs() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Literal(IrLit::Unit))
            };

            let op = if let Some(op_token) = bin_expr.op_kind() {
                match op_token {
                    ra_ap_syntax::ast::BinaryOp::ArithOp(arith_op) => match arith_op {
                        ra_ap_syntax::ast::ArithOp::Add => BinOp::Add,
                        ra_ap_syntax::ast::ArithOp::Sub => BinOp::Sub,
                        ra_ap_syntax::ast::ArithOp::Mul => BinOp::Mul,
                        ra_ap_syntax::ast::ArithOp::Div => BinOp::Div,
                        ra_ap_syntax::ast::ArithOp::Rem => BinOp::Rem,
                        _ => BinOp::Add,
                    },
                    ra_ap_syntax::ast::BinaryOp::CmpOp(cmp_op) => match cmp_op {
                        ra_ap_syntax::ast::CmpOp::Eq { .. } => BinOp::Eq,
                        ra_ap_syntax::ast::CmpOp::Ord { .. } => BinOp::Lt, // Simplified - would need to extract actual ordering
                    },
                    ra_ap_syntax::ast::BinaryOp::LogicOp(logic_op) => match logic_op {
                        ra_ap_syntax::ast::LogicOp::And => BinOp::And,
                        ra_ap_syntax::ast::LogicOp::Or => BinOp::Or,
                    },
                    _ => BinOp::Add,
                }
            } else {
                BinOp::Add
            };

            IrExpr::Binary { op, lhs, rhs }
        }
        Expr::IfExpr(if_expr) => {
            let cond = if let Some(condition) = if_expr.condition() {
                Box::new(convert_ast_expr_with_types(sema, &condition))
            } else {
                Box::new(IrExpr::Literal(IrLit::Bool(true)))
            };

            let then_branch = if let Some(then_block) = if_expr.then_branch() {
                Box::new(convert_ast_block_to_ir(sema, &then_block))
            } else {
                Box::new(crate::rust_ir::Block {
                    statements: Vec::new(),
                    tail: None,
                })
            };

            let else_branch = if_expr.else_branch().map(|else_branch| {
                // Handle different else branch types (ElseBranch can be IfExpr or BlockExpr)
                match else_branch {
                    ra_ap_syntax::ast::ElseBranch::Block(block) => {
                        Box::new(convert_ast_block_to_ir(sema, &block))
                    }
                    ra_ap_syntax::ast::ElseBranch::IfExpr(else_if) => {
                        // Convert else if to a block containing the if expression
                        Box::new(crate::rust_ir::Block {
                            statements: Vec::new(),
                            tail: Some(Box::new(convert_ast_expr_with_types(
                                sema,
                                &ra_ap_syntax::ast::Expr::IfExpr(else_if),
                            ))),
                        })
                    }
                }
            });

            IrExpr::If {
                cond,
                then_branch,
                else_branch,
            }
        }
        Expr::BlockExpr(block_expr) => {
            IrExpr::BlockExpr(Box::new(convert_ast_block_to_ir(sema, block_expr)))
        }
        // Handle other expression types with placeholders
        _ => IrExpr::Literal(IrLit::Unit),
    }
}

/// Convert HIR blocks to RustIR blocks (legacy function for HIR-def bodies)
fn convert_block(
    _sema: &Semantics<RootDatabase>,
    _body: &ra_ap_hir_def::expr_store::Body,
) -> crate::rust_ir::Block {
    // For now, return a placeholder - this function is for HIR-def bodies
    // Most function bodies should go through convert_ast_block_to_ir instead
    crate::rust_ir::Block {
        statements: Vec::new(),
        tail: None,
    }
}

// ============================================================================
// Syntax-only fallback converters (no type information)
// ============================================================================

/// Fallback function converter when HIR resolution fails
fn convert_function_syntax_fallback(fn_def: &ast::Fn) -> IrFunction {
    let name = fn_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "unnamed".to_string());

    // Extract parameters without type information
    let params = fn_def
        .param_list()
        .map(|list| {
            list.params()
                .map(|param| {
                    let name = param
                        .pat()
                        .and_then(|pat| match pat {
                            ast::Pat::IdentPat(ident) => ident.name().map(|n| n.to_string()),
                            _ => None,
                        })
                        .unwrap_or_else(|| "param".to_string());

                    // Without HIR, we can only get syntax-level type info
                    let ty = param
                        .ty()
                        .map(|type_ref| convert_type_ref_syntax(&type_ref))
                        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

                    (name, ty)
                })
                .collect()
        })
        .unwrap_or_else(Vec::new);

    // Extract return type without semantic analysis
    let return_type = fn_def
        .ret_type()
        .and_then(|ret| ret.ty())
        .map(|type_ref| convert_type_ref_syntax(&type_ref));

    // Extract generics (syntax-only)
    let generics = fn_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    // Create empty body for now (syntax parsing would be complex)
    let body = crate::rust_ir::Block {
        statements: Vec::new(),
        tail: None,
    };

    IrFunction {
        name,
        generics,
        params,
        return_type,
        body,
    }
}

/// Convert syntax TypeRef to our Type system (no semantic info)
fn convert_type_ref_syntax(type_ref: &ast::Type) -> crate::rust_ir::Type {
    // This is a simplified conversion that only looks at syntax
    use crate::rust_ir::Type;

    match type_ref {
        ast::Type::PathType(path_type) => {
            if let Some(path) = path_type.path() {
                let segments = path
                    .segments()
                    .map(|seg| {
                        seg.name_ref()
                            .map(|n| n.to_string())
                            .unwrap_or_else(|| "unknown".to_string())
                    })
                    .collect();
                Type::Path(segments)
            } else {
                Type::Path(vec!["unknown".to_string()])
            }
        }
        ast::Type::RefType(ref_type) => {
            let mutable = ref_type.mut_token().is_some();
            let inner = ref_type
                .ty()
                .map(|inner_ty| Box::new(convert_type_ref_syntax(&inner_ty)))
                .unwrap_or_else(|| Box::new(Type::Path(vec!["unknown".to_string()])));
            Type::Reference { mutable, inner }
        }
        _ => {
            // Fallback for complex types we don't handle yet
            Type::Path(vec!["unknown".to_string()])
        }
    }
}

/// Convert syntax generic parameter to our GenericParam (no bounds info)
fn convert_generic_param_syntax(param: &ast::GenericParam) -> crate::rust_ir::GenericParam {
    let name = match param {
        ast::GenericParam::TypeParam(type_param) => type_param
            .name()
            .map(|n| n.to_string())
            .unwrap_or_else(|| "T".to_string()),
        ast::GenericParam::LifetimeParam(lifetime_param) => lifetime_param
            .lifetime()
            .map(|lt| lt.to_string())
            .unwrap_or_else(|| "'a".to_string()),
        ast::GenericParam::ConstParam(const_param) => const_param
            .name()
            .map(|n| n.to_string())
            .unwrap_or_else(|| "N".to_string()),
    };

    crate::rust_ir::GenericParam {
        name,
        bounds: Vec::new(), // Can't resolve bounds without semantics
    }
}

/// Syntax-only fallback converters for other item types
fn convert_struct_syntax_fallback(struct_def: &ast::Struct) -> crate::rust_ir::StructDef {
    let name = struct_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedStruct".to_string());

    let generics = struct_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let fields = struct_def
        .field_list()
        .map(|field_list| match field_list {
            ast::FieldList::RecordFieldList(record_fields) => record_fields
                .fields()
                .map(|field| crate::rust_ir::StructField {
                    name: field
                        .name()
                        .map(|n| n.to_string())
                        .unwrap_or_else(|| "field".to_string()),
                    ty: field
                        .ty()
                        .map(|ty| convert_type_ref_syntax(&ty))
                        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()])),
                })
                .collect(),
            _ => Vec::new(), // Tuple structs would need different handling
        })
        .unwrap_or_else(Vec::new);

    crate::rust_ir::StructDef {
        name,
        generics,
        fields,
    }
}

fn convert_enum_syntax_fallback(enum_def: &ast::Enum) -> crate::rust_ir::EnumDef {
    let name = enum_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedEnum".to_string());

    let generics = enum_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let variants = enum_def
        .variant_list()
        .map(|variant_list| {
            variant_list
                .variants()
                .map(|variant| crate::rust_ir::VariantDef {
                    name: variant
                        .name()
                        .map(|n| n.to_string())
                        .unwrap_or_else(|| "Variant".to_string()),
                    payload: crate::rust_ir::VariantPayload::Unit, // Simplified - would need more work for full support
                })
                .collect()
        })
        .unwrap_or_else(Vec::new);

    crate::rust_ir::EnumDef {
        name,
        generics,
        variants,
    }
}

fn convert_const_syntax_fallback(const_def: &ast::Const) -> crate::rust_ir::ConstantDef {
    let name = const_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UNNAMED".to_string());

    let ty = const_def
        .ty()
        .map(|type_ref| convert_type_ref_syntax(&type_ref))
        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

    // Without semantics, we can't evaluate the expression properly
    let expr = crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit);

    crate::rust_ir::ConstantDef { name, ty, expr }
}

fn convert_type_alias_syntax_fallback(
    type_alias_def: &ast::TypeAlias,
) -> crate::rust_ir::TypeAliasDef {
    let name = type_alias_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedType".to_string());

    let generics = type_alias_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let aliased = type_alias_def
        .ty()
        .map(|type_ref| convert_type_ref_syntax(&type_ref))
        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

    crate::rust_ir::TypeAliasDef {
        name,
        generics,
        aliased,
    }
}
```

## main.rs

```rust
// src/main.rs
mod cli;
mod hir_analysis;
mod rust_ir;
mod ra_loader;

use anyhow::Result;
use clap::Parser;
use cli::Cli;

fn main() -> Result<()> {
    let args = Cli::parse();

    // New: analyze the workspace directly (typed HIR)
    let ir_crate = hir_analysis::analyze_workspace(&args.rust_project_dir)?;

    if let Some(path) = args.dump_rust_ir.as_ref() {
        ir_crate.serialize_as_code(path)?;
    }

    Ok(())
}
```

## ra_loader.rs

```rust
// src/ra_loader.rs
use anyhow::Result;
use ra_ap_load_cargo::{load_workspace_at, LoadCargoConfig, ProcMacroServerChoice};
use ra_ap_project_model::CargoConfig;
use ra_ap_ide_db::RootDatabase;
use ra_ap_vfs::Vfs;
use std::path::Path;

pub fn load_workspace_db(project_root: &Path, enable_proc_macros: bool) -> Result<(RootDatabase, Vfs)> {
    // CargoConfig *does* have Default
    let cargo_cfg = CargoConfig::default();

    // LoadCargoConfig has no Default in 0.0.279; build it explicitly
    let load_cfg = LoadCargoConfig {
        load_out_dirs_from_check: true,
        prefill_caches: true,
        with_proc_macro_server: if enable_proc_macros {
            ProcMacroServerChoice::Sysroot
        } else {
            ProcMacroServerChoice::None
        },
    };

    let (db, vfs, _pm) = load_workspace_at(project_root, &cargo_cfg, &load_cfg, &|_progress| {})?;
    Ok((db, vfs))
}
```

## rust_ir.rs

```rust
//! RustIR AST definitions — a simplified Rust for WP computation
//!
//! RustIR is an intermediate, desugared representation of rust code. In this
//! form, the code is easier to analyze and process using tools like WP the
//! algorithm.

use std::fmt::{self, Display, Formatter};

/// A complete RustIR crate/module
pub struct Crate {
    /// Top-level items in the crate
    pub items: Vec<Item>,
}

impl Crate {
    pub fn new() -> Self {
        Self { items: Vec::new() }
    }

    pub fn append(&mut self, krate: &mut Crate) {
        self.items.append(&mut krate.items);
    }
    
    /// Serialize the crate as Rust-like code to a file
    pub fn serialize_as_code<P: AsRef<std::path::Path>>(&self, path: P) -> std::io::Result<()> {
        use std::fs::File;
        use std::io::Write;

        println!("Serializing RustIR to {}; top-level items: {}", path.as_ref().display(), self.items.len());
        
        let mut file = File::create(path)?;
        write!(file, "{}", self)?;
        Ok(())
    }
}

/// All kinds of top-level definitions
pub enum Item {
    TypeAlias(TypeAliasDef),
    Struct(StructDef),
    Enum(EnumDef),
    Trait(TraitDef),
    Impl(ImplDef),
    Function(FunctionDef),
    Constant(ConstantDef),
}

/// Generic parameter with optional trait bounds
pub struct GenericParam {
    pub name: String,
    pub bounds: Vec<Type>,
}

/// A type alias: `type Name<Params> = Type;`
pub struct TypeAliasDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub aliased: Type,
}

/// A struct definition: `struct Name<Params> { fields }`
pub struct StructDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub fields: Vec<StructField>,
}

pub struct StructField {
    pub name: String,
    pub ty: Type,
}

/// An enum definition: `enum Name<Params> { variants }`
pub struct EnumDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub variants: Vec<VariantDef>,
}

pub struct VariantDef {
    pub name: String,
    pub payload: VariantPayload,
}

pub enum VariantPayload {
    /// No data
    Unit,
    /// Tuple-like `Variant(T1, T2, ...)`
    Tuple(Vec<Type>),
    /// Struct-like `Variant { f1: T1, f2: T2 }`
    Struct(Vec<StructField>),
}

/// A trait definition with supertraits and items
pub struct TraitDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub supertraits: Vec<Type>,
    pub items: Vec<TraitItem>,
}

pub enum TraitItem {
    Method(FunctionSig),
    TypeAlias(TypeAliasDef),
    Const(ConstantSig),
}

/// An impl block, optionally for a trait
pub struct ImplDef {
    /// None = inherent impl; Some(t) = impl of trait t
    pub trait_type: Option<Type>,
    pub self_ty: Type,
    pub generics: Vec<GenericParam>,
    pub items: Vec<ImplItem>,
}

pub enum ImplItem {
    Method(FunctionDef),
    TypeAlias(TypeAliasDef),
    Const(ConstantDef),
}

/// A free-standing function
pub struct FunctionDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub params: Vec<(String, Type)>,
    pub return_type: Option<Type>,
    pub body: Block,
}

/// Function signature (no body)
pub struct FunctionSig {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub params: Vec<(String, Type)>,
    pub return_type: Option<Type>,
}

/// A constant definition with initializer
pub struct ConstantDef {
    pub name: String,
    pub ty: Type,
    pub expr: Expr,
}

/// Constant signature (no expr)
pub struct ConstantSig {
    pub name: String,
    pub ty: Type,
}

/// A block is a sequence of statements ending in an optional expression
pub struct Block {
    pub statements: Vec<Stmt>,
    pub tail: Option<Box<Expr>>,
}

/// Statements in RustIR
pub enum Stmt {
    /// `let x [: Type] [= Expr];`
    Let {
        name: String,
        ty: Option<Type>,
        init: Option<Expr>,
    },
    /// An expression used as a statement
    Expr(Expr),
    /// `assert!(Expr)` or custom assume/assert
    Assert(Expr),
    /// `return [Expr]`
    Return(Option<Expr>),
    /// `break` / `continue`
    Break,
    Continue,
}

/// All expression forms
pub enum Expr {
    /// Literal values
    Literal(Literal),
    /// Variable or path reference, e.g. `x` or `MyType::CONST`
    Path(Vec<String>),
    /// Function or constructor call
    Call {
        callee: Box<Expr>,
        args: Vec<Expr>,
    },
    /// Method call `e.method(args)`
    MethodCall {
        receiver: Box<Expr>,
        method: String,
        args: Vec<Expr>,
    },
    /// Field access `e.field`
    FieldAccess {
        target: Box<Expr>,
        field: String,
    },
    /// Indexing `e[index]`
    Index {
        target: Box<Expr>,
        index: Box<Expr>,
    },
    /// Binary operations: `lhs op rhs`
    Binary {
        op: BinOp,
        lhs: Box<Expr>,
        rhs: Box<Expr>,
    },
    /// Unary operations: `op expr`
    Unary {
        op: UnOp,
        expr: Box<Expr>,
    },
    /// `if cond { then } else { else }`
    If {
        cond: Box<Expr>,
        then_branch: Box<Block>,
        else_branch: Option<Box<Block>>,
    },
    /// `while cond { body }`
    While {
        cond: Box<Expr>,
        body: Box<Block>,
    },
    /// `match expr { arms }`
    Match {
        expr: Box<Expr>,
        arms: Vec<(Pattern, Box<Block>)>,
    },
    /// Block expression
    BlockExpr(Box<Block>),
    /// Cast `expr as Type`
    Cast {
        expr: Box<Expr>,
        ty: Type,
    },
    /// Loop without condition (infinite loop)
    Loop(Box<Block>),
}

/// Patterns for match arms
pub enum Pattern {
    Wildcard,
    Ident(String),
    Literal(Literal),
    Tuple(Vec<Pattern>),
    Struct {
        path: Vec<String>,
        fields: Vec<(String, Pattern)>,
    },
    TupleStruct {
        path: Vec<String>,
        elems: Vec<Pattern>,
    },
}

/// Literal constants
pub enum Literal {
    Int(i64),
    Bool(bool),
    Float(f64),
    Char(char),
    String(String),
    Unit,
}

/// Binary operators
pub enum BinOp {
    Add,
    Sub,
    Mul,
    Div,
    Rem,
    And,
    Or,
    Eq,
    NotEq,
    Lt,
    Le,
    Gt,
    Ge,
}

/// Unary operators
pub enum UnOp {
    Neg,
    Not,
}

/// Types in RustIR
pub enum Type {
    /// Primitive types (u32, i64, bool, etc.)
    Primitive(String),
    /// Named path, e.g. `std::vec::Vec`
    Path(Vec<String>),
    /// Generic type instantiation, e.g. `Option<T>`
    Generic {
        base: Box<Type>,
        args: Vec<Type>,
    },
    /// Type parameter
    TypeParam(String),
    /// Reference `&T` or `&mut T`
    Reference {
        mutable: bool,
        inner: Box<Type>,
    },
    /// Array `[T; N]` or slice `[T]`
    Array {
        inner: Box<Type>,
        len: Option<usize>,
    },
    /// Tuple `(T1, T2, ...)`
    Tuple(Vec<Type>),
    /// Function pointer `(T1, T2) -> Tret`
    FnPtr {
        params: Vec<Type>,
        ret: Box<Type>,
    },
    /// Associated type abstraction (e.g. `<T as Trait>::Assoc`)
    AssocType {
        type_: Box<Type>,
        name: String,
    },
}

// ============================================================================
// Display implementations for debug pretty printing
// ============================================================================

/// Helper struct for indented display
struct IndentedDisplay<'a, T: Display>(&'a T, usize);

impl<T: Display> Display for IndentedDisplay<'_, T> {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let content = format!("{}", self.0);
        for line in content.lines() {
            writeln!(f, "{}{}", "\t".repeat(self.1), line)?;
        }
        Ok(())
    }
}

impl Display for Crate {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        for (i, item) in self.items.iter().enumerate() {
            if i > 0 {
                writeln!(f)?;
            }
            write!(f, "{}", item)?;
        }
        Ok(())
    }
}

impl Display for Item {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Item::TypeAlias(ta) => write!(f, "{}", ta),
            Item::Struct(s) => write!(f, "{}", s),
            Item::Enum(e) => write!(f, "{}", e),
            Item::Trait(t) => write!(f, "{}", t),
            Item::Impl(i) => write!(f, "{}", i),
            Item::Function(func) => write!(f, "{}", func),
            Item::Constant(c) => write!(f, "{}", c),
        }
    }
}

impl Display for FunctionDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "fn {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        write!(f, "(")?;
        for (i, (name, ty)) in self.params.iter().enumerate() {
            if i > 0 { write!(f, ", ")?; }
            write!(f, "{}: {}", name, ty)?;
        }
        write!(f, ")")?;
        
        if let Some(ret) = &self.return_type {
            write!(f, " -> {}", ret)?;
        }
        
        writeln!(f, " {{")?;
        write!(f, "{}", IndentedDisplay(&self.body, 1))?;
        write!(f, "}}")
    }
}

impl Display for StructDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "struct {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        if self.fields.is_empty() {
            write!(f, ";")?;
        } else {
            writeln!(f, " {{")?;
            for field in &self.fields {
                writeln!(f, "\t{}: {},", field.name, field.ty)?;
            }
            write!(f, "}}")?;
        }
        Ok(())
    }
}

impl Display for EnumDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "enum {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        writeln!(f, " {{")?;
        for variant in &self.variants {
            writeln!(f, "\t{},", variant)?;
        }
        write!(f, "}}")
    }
}

impl Display for VariantDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)?;
        match &self.payload {
            VariantPayload::Unit => Ok(()),
            VariantPayload::Tuple(types) => {
                write!(f, "(")?;
                for (i, ty) in types.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", ty)?;
                }
                write!(f, ")")
            }
            VariantPayload::Struct(fields) => {
                writeln!(f, " {{")?;
                for field in fields {
                    writeln!(f, "\t\t{}: {},", field.name, field.ty)?;
                }
                write!(f, "\t}}")
            }
        }
    }
}

impl Display for TypeAliasDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "type {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        write!(f, " = {};", self.aliased)
    }
}

impl Display for ConstantDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "const {}: {} = {};", self.name, self.ty, self.expr)
    }
}

impl Display for TraitDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "trait {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        if !self.supertraits.is_empty() {
            write!(f, ": ")?;
            for (i, st) in self.supertraits.iter().enumerate() {
                if i > 0 { write!(f, " + ")?; }
                write!(f, "{}", st)?;
            }
        }
        
        writeln!(f, " {{")?;
        for item in &self.items {
            write!(f, "{}", IndentedDisplay(item, 1))?;
        }
        write!(f, "}}")
    }
}

impl Display for TraitItem {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            TraitItem::Method(sig) => write!(f, "{};", sig),
            TraitItem::TypeAlias(ta) => write!(f, "{}", ta),
            TraitItem::Const(c) => write!(f, "{};", c),
        }
    }
}

impl Display for FunctionSig {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "fn {}", self.name)?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        write!(f, "(")?;
        for (i, (name, ty)) in self.params.iter().enumerate() {
            if i > 0 { write!(f, ", ")?; }
            write!(f, "{}: {}", name, ty)?;
        }
        write!(f, ")")?;
        
        if let Some(ret) = &self.return_type {
            write!(f, " -> {}", ret)?;
        }
        
        Ok(())
    }
}

impl Display for ConstantSig {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "const {}: {}", self.name, self.ty)
    }
}

impl Display for ImplDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "impl")?;
        
        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 { write!(f, ", ")?; }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }
        
        if let Some(trait_ty) = &self.trait_type {
            write!(f, " {} for", trait_ty)?;
        }
        
        write!(f, " {}", self.self_ty)?;
        
        writeln!(f, " {{")?;
        for item in &self.items {
            write!(f, "{}", IndentedDisplay(item, 1))?;
            writeln!(f)?;
        }
        write!(f, "}}")
    }
}

impl Display for ImplItem {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            ImplItem::Method(func) => write!(f, "{}", func),
            ImplItem::TypeAlias(ta) => write!(f, "{}", ta),
            ImplItem::Const(c) => write!(f, "{}", c),
        }
    }
}

impl Display for GenericParam {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)?;
        if !self.bounds.is_empty() {
            write!(f, ": ")?;
            for (i, bound) in self.bounds.iter().enumerate() {
                if i > 0 { write!(f, " + ")?; }
                write!(f, "{}", bound)?;
            }
        }
        Ok(())
    }
}

impl Display for Block {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        for stmt in &self.statements {
            writeln!(f, "{}", stmt)?;
        }
        if let Some(tail) = &self.tail {
            write!(f, "{}", tail)?;
        }
        Ok(())
    }
}

impl Display for Stmt {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Stmt::Let { name, ty, init } => {
                write!(f, "let {}", name)?;
                if let Some(ty) = ty {
                    write!(f, ": {}", ty)?;
                }
                if let Some(init) = init {
                    write!(f, " = {}", init)?;
                }
                write!(f, ";")
            }
            Stmt::Expr(expr) => write!(f, "{};", expr),
            Stmt::Assert(expr) => write!(f, "assert!({});", expr),
            Stmt::Return(expr) => {
                write!(f, "return")?;
                if let Some(e) = expr {
                    write!(f, " {}", e)?;
                }
                write!(f, ";")
            }
            Stmt::Break => write!(f, "break;"),
            Stmt::Continue => write!(f, "continue;"),
        }
    }
}

impl Display for Expr {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Expr::Literal(lit) => write!(f, "{}", lit),
            Expr::Path(segments) => write!(f, "{}", segments.join("::")),
            Expr::Call { callee, args } => {
                write!(f, "{}(", callee)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", arg)?;
                }
                write!(f, ")")
            }
            Expr::MethodCall { receiver, method, args } => {
                write!(f, "{}.{}(", receiver, method)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", arg)?;
                }
                write!(f, ")")
            }
            Expr::FieldAccess { target, field } => write!(f, "{}.{}", target, field),
            Expr::Index { target, index } => write!(f, "{}[{}]", target, index),
            Expr::Binary { op, lhs, rhs } => write!(f, "({} {} {})", lhs, op, rhs),
            Expr::Unary { op, expr } => write!(f, "{}{}", op, expr),
            Expr::If { cond, then_branch, else_branch } => {
                write!(f, "if {} {{\n", cond)?;
                write!(f, "{}", IndentedDisplay(then_branch.as_ref(), 1))?;
                write!(f, "}}")?;
                if let Some(else_br) = else_branch {
                    write!(f, " else {{\n")?;
                    write!(f, "{}", IndentedDisplay(else_br.as_ref(), 1))?;
                    write!(f, "}}")?;
                }
                Ok(())
            }
            Expr::While { cond, body } => {
                write!(f, "while {} {{\n", cond)?;
                write!(f, "{}", IndentedDisplay(body.as_ref(), 1))?;
                write!(f, "}}")
            }
            Expr::Match { expr, arms } => {
                write!(f, "match {} {{\n", expr)?;
                for (pattern, block) in arms {
                    write!(f, "\t{} => {{\n", pattern)?;
                    write!(f, "{}", IndentedDisplay(block.as_ref(), 2))?;
                    writeln!(f, "\t}},")?;
                }
                write!(f, "}}")
            }
            Expr::BlockExpr(block) => {
                writeln!(f, "{{")?;
                write!(f, "{}", IndentedDisplay(block.as_ref(), 1))?;
                write!(f, "}}")
            }
            Expr::Cast { expr, ty } => write!(f, "{} as {}", expr, ty),
            Expr::Loop(body) => {
                write!(f, "loop {{\n")?;
                write!(f, "{}", IndentedDisplay(body.as_ref(), 1))?;
                write!(f, "}}")
            }
        }
    }
}

impl Display for Pattern {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Pattern::Wildcard => write!(f, "_"),
            Pattern::Ident(name) => write!(f, "{}", name),
            Pattern::Literal(lit) => write!(f, "{}", lit),
            Pattern::Tuple(patterns) => {
                write!(f, "(")?;
                for (i, pat) in patterns.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", pat)?;
                }
                write!(f, ")")
            }
            Pattern::Struct { path, fields } => {
                write!(f, "{} {{ ", path.join("::"))?;
                for (i, (name, pat)) in fields.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}: {}", name, pat)?;
                }
                write!(f, " }}")
            }
            Pattern::TupleStruct { path, elems } => {
                write!(f, "{}(", path.join("::"))?;
                for (i, elem) in elems.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", elem)?;
                }
                write!(f, ")")
            }
        }
    }
}

impl Display for Literal {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Literal::Int(i) => write!(f, "{}", i),
            Literal::Bool(b) => write!(f, "{}", b),
            Literal::Float(fl) => write!(f, "{}", fl),
            Literal::Char(c) => write!(f, "'{}'", c),
            Literal::String(s) => write!(f, "\"{}\"", s),
            Literal::Unit => write!(f, "()"),
        }
    }
}

impl Display for BinOp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let op = match self {
            BinOp::Add => "+",
            BinOp::Sub => "-",
            BinOp::Mul => "*",
            BinOp::Div => "/",
            BinOp::Rem => "%",
            BinOp::And => "&&",
            BinOp::Or => "||",
            BinOp::Eq => "==",
            BinOp::NotEq => "!=",
            BinOp::Lt => "<",
            BinOp::Le => "<=",
            BinOp::Gt => ">",
            BinOp::Ge => ">=",
        };
        write!(f, "{}", op)
    }
}

impl Display for UnOp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let op = match self {
            UnOp::Neg => "-",
            UnOp::Not => "!",
        };
        write!(f, "{}", op)
    }
}

impl Display for Type {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Type::Primitive(name) => write!(f, "{}", name),
            Type::Path(segments) => write!(f, "{}", segments.join("::")),
            Type::Generic { base, args } => {
                write!(f, "{}<", base)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", arg)?;
                }
                write!(f, ">")
            }
            Type::TypeParam(name) => write!(f, "{}", name),
            Type::Reference { mutable, inner } => {
                if *mutable {
                    write!(f, "&mut {}", inner)
                } else {
                    write!(f, "&{}", inner)
                }
            }
            Type::Array { inner, len } => {
                if let Some(len) = len {
                    write!(f, "[{}; {}]", inner, len)
                } else {
                    write!(f, "[{}]", inner)
                }
            }
            Type::Tuple(types) => {
                write!(f, "(")?;
                for (i, ty) in types.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", ty)?;
                }
                if types.len() == 1 {
                    write!(f, ",")?; // Single-element tuple needs trailing comma
                }
                write!(f, ")")
            }
            Type::FnPtr { params, ret } => {
                write!(f, "fn(")?;
                for (i, param) in params.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", param)?;
                }
                write!(f, ") -> {}", ret)
            }
            Type::AssocType { type_, name } => write!(f, "<{} as _>::{}", type_, name),
        }
    }
}
```

