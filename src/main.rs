// src/main.rs
mod cli;
mod hir_analysis;
mod rust_ir;
mod ra_loader;

use anyhow::Result;
use clap::Parser;
use cli::Cli;

fn main() -> Result<()> {
    let args = Cli::parse();

    // New: analyze the workspace directly (typed HIR)
    let ir_crate = hir_analysis::analyze_workspace(&args.rust_project_dir)?;

    if let Some(path) = args.dump_rust_ir.as_ref() {
        ir_crate.serialize_as_code(path)?;
    }

    Ok(())
}