// src/ra_loader.rs
use anyhow::{bail, Context, Result};
use ra_ap_ide_db::RootDatabase;
use ra_ap_load_cargo::{load_workspace_at, LoadCargoConfig, ProcMacroServerChoice};
use ra_ap_project_model::CargoConfig;
use ra_ap_vfs::Vfs;
use std::path::{Path, PathBuf};

fn manifest_path_from(project_root: &Path) -> Result<PathBuf> {
    if project_root.is_file() {
        Ok(project_root.to_path_buf())
    } else {
        let m = project_root.join("Cargo.toml");
        if m.is_file() {
            Ok(m)
        } else {
            bail!("Could not find Cargo.toml at {}", m.display());
        }
    }
}

pub fn load_workspace_db(project_root: &Path, enable_proc_macros: bool) -> Result<(RootDatabase, Vfs)> {
    let manifest = manifest_path_from(project_root)?;
    let cargo_cfg = CargoConfig::default();

    let load_cfg = LoadCargoConfig {
        load_out_dirs_from_check: true,
        prefill_caches: true,
        with_proc_macro_server: if enable_proc_macros {
            ProcMacroServerChoice::Sysroot
        } else {
            ProcMacroServerChoice::None
        },
    };

    let (db, vfs, _pm) = load_workspace_at(&manifest, &cargo_cfg, &load_cfg, &|_p| {})
        .with_context(|| format!("load_workspace_at({}) failed", manifest.display()))?;
    Ok((db, vfs))
}