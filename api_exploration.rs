//! Temporary file to explore HIR API capabilities
//! This file helps understand how to work with HIR expressions in ra_ap_hir version 0.0.297

use ra_ap_hir::{Semantics, Function};
use ra_ap_hir_def;
use ra_ap_ide_db::RootDatabase;

fn explore_function_api() {
    // Create a database for exploration
    let db = RootDatabase::default();
    let sema = Semantics::new(&db);
    
    // This file explores what methods are available on Function
    // and how to access function bodies and expressions
    
    // When we have a Function, what methods are available?
    let function: Function = todo!();
    
    // These are the methods we need to find:
    
    // 1. How to get the function body?
    // Possible patterns based on rust-analyzer:
    // - function.body(&db) ?
    // - sema.body_of_function(function) ?
    // - db.body(function.into()) ?
    
    // 2. How to get expressions from a body?
    // - body.expressions() ?
    // - body.expr_for_id(expr_id) ?
    
    // 3. How to traverse expression trees?
    // - match expr { ... } for each Expr variant
    
    // 4. How to get the HIR definition ID for a function?
    // - function.into() to convert to DefWithBodyId ?
}

fn explore_hir_def_types() {
    // Explore what's available in ra_ap_hir_def
    use ra_ap_hir_def::hir::{Expr, ExprId, Body};
    use ra_ap_hir_def::DefWithBodyId;
    
    // What expression variants are available?
    let expr: Expr = todo!();
    match expr {
        // We need to see what variants are available in Expr
        _ => {}
    }
    
    // How to work with Body and ExprId?
    let body: Body = todo!();
    let expr_id: ExprId = body.body_expr; // Is this available?
    
    // How to get an expression from a body using ExprId?
    let expr_from_body = body.exprs[expr_id]; // Is indexing available?
}